# 🚀 Flutter Hızlı Kurulum Rehberi

## ❌ Mevcut Durum
Flutter sisteminizde kurulu değil. Aşağıdaki adımları takip ederek kurabilirsiniz.

## 📥 1. Flutter SDK İndirme

### Windows için:
1. **Flutter'ı indirin**: https://docs.flutter.dev/get-started/install/windows
2. **"flutter_windows_3.x.x-stable.zip"** dosyasını indirin
3. **C:\ dizinine çıkarın** (örn: `C:\flutter`)

## 🔧 2. PATH Değişkeni Ayarlama

### Otomatik Yöntem:
```cmd
setx PATH "%PATH%;C:\flutter\bin"
```

### Manuel Yöntem:
1. **Windows tuşu + R** → `sysdm.cpl` yazın
2. **Advanced** → **Environment Variables**
3. **System Variables** → **Path** → **Edit**
4. **New** → `C:\flutter\bin` yazın
5. **OK** → **OK** → **OK**

## 🔄 3. Terminal Yeniden Başlatma
- **Mevcut terminal'i kapatın**
- **Yeni terminal açın**
- **Proje klasörüne gidin**

## ✅ 4. Flutter Kontrol
```cmd
flutter doctor
```

Bu komut Flutter'ın doğru kurulup kurulmadığını kontrol eder.

## 📱 5. Android Studio (Önerilen)
1. **Android Studio'yu indirin**: https://developer.android.com/studio
2. **Kurulumu tamamlayın**
3. **Android SDK'yı yükleyin**
4. **Virtual Device oluşturun**

## 🚀 6. Uygulamayı Çalıştırma

### Proje klasöründe:
```cmd
flutter pub get
flutter run
```

## 🎯 Hızlı Test

Flutter kurulduktan sonra:

1. **Terminal açın**
2. **Proje klasörüne gidin**
3. **Şu komutu çalıştırın**:
```cmd
quick_run.bat
```

## 🐛 Sorun Giderme

### Flutter bulunamıyor:
- PATH değişkenini kontrol edin
- Terminal'i yeniden başlatın
- `where flutter` komutu ile konumu kontrol edin

### Android lisans hataları:
```cmd
flutter doctor --android-licenses
```

### Bağımlılık hataları:
```cmd
flutter clean
flutter pub get
```

## 📞 Yardım

Kurulum tamamlandıktan sonra:
- ✅ `flutter --version` çalışmalı
- ✅ `flutter doctor` yeşil tik göstermeli
- ✅ `flutter devices` cihazları listelelemeli

---

**Not**: Kurulum yaklaşık 10-15 dakika sürebilir. İnternet bağlantınızın iyi olduğundan emin olun.
