# Mürşid - Vatandaş İhbar Uygulaması

Mürşid, vatandaşların toplumsal güvenliğe katkıda bulunmasını sağlayan bir mobil uygulamadır. Kullanıcılar çeşitli ihlalleri fotoğraf veya video ile belgeleyerek ilgili makamlara bildirebilir ve onaylanan ihbarlardan ödül kazanabilirler.

## 🚀 Özellikler

### Kullanıcı Özellikleri
- **Güvenli Giriş**: E-posta ve şifre ile güvenli giriş sistemi
- **İhbar Oluşturma**: Kamera veya galeriden fotoğraf/video ile ihbar
- **İhlal Türleri**: Trafik, temizlik, taciz, şiddet, gürültü, park ihlalleri
- **Konum Entegrasyonu**: Otomatik konum tespiti
- **İhbar <PERSON>**: Gönderilen ihbarların durumunu takip etme
- **<PERSON><PERSON>**: Onaylanan ihbarlardan %20 ödül
- **Cüzdan**: Kazançları görüntüleme ve para çekme

### Teknik Özellikler
- **Flutter Framework**: Modern ve performanslı UI
- **Material 3 Design**: Google'ın en yeni tasarım dili
- **Çoklu Dil Desteği**: Türkçe (ana dil), Arapça, İngilizce
- **Karanlık Mod**: Göz dostu karanlık tema
- **Responsive Design**: Tüm ekran boyutlarına uyumlu

## 📱 Ekran Görüntüleri

### Ana Ekranlar
- Giriş/Kayıt Ekranları
- Ana Sayfa (İhbar butonu ve istatistikler)
- Yeni İhbar Oluşturma
- İhbar Geçmişi
- Cüzdan ve Kazançlar
- Profil ve Ayarlar

## 🛠️ Kurulum

### Gereksinimler
- Flutter SDK (3.0.0 veya üzeri)
- Dart SDK
- Android Studio / VS Code
- Android SDK (Android geliştirme için)
- Xcode (iOS geliştirme için)

### Adımlar

1. **Projeyi klonlayın**
```bash
git clone <repository-url>
cd مرشد
```

2. **Bağımlılıkları yükleyin**
```bash
flutter pub get
```

3. **Code generation çalıştırın**
```bash
flutter packages pub run build_runner build
```

4. **Google Maps API Key ekleyin**
- `android/app/src/main/AndroidManifest.xml` dosyasında `YOUR_GOOGLE_MAPS_API_KEY` yerine gerçek API key'inizi yazın
- iOS için `ios/Runner/AppDelegate.swift` dosyasını güncelleyin

5. **Uygulamayı çalıştırın**
```bash
flutter run
```

## 🏗️ Proje Yapısı

```
lib/
├── core/                    # Temel yapılar
│   ├── theme/              # Tema ve renkler
│   ├── router/             # Navigasyon
│   └── constants/          # Sabitler
├── features/               # Özellik modülleri
│   ├── auth/              # Kimlik doğrulama
│   ├── home/              # Ana sayfa
│   ├── report/            # İhbar oluşturma
│   ├── reports/           # İhbar listesi
│   ├── wallet/            # Cüzdan
│   └── profile/           # Profil
├── models/                # Veri modelleri
├── services/              # API servisleri
└── shared/                # Paylaşılan bileşenler
```

## 🎨 Tasarım Sistemi

### Renkler
- **Ana Renk**: Mavi (#1976D2)
- **İkincil Renk**: Yeşil (#388E3C)
- **İhlal Türü Renkleri**: Her ihlal türü için özel renk
- **Durum Renkleri**: Beklemede (Turuncu), Onaylandı (Yeşil), Reddedildi (Kırmızı)

### Tipografi
- **Ana Font**: Roboto
- **Başlıklar**: Bold ağırlık
- **Gövde Metni**: Regular ağırlık

## 🌐 Çoklu Dil Desteği

Uygulama şu dilleri destekler:
- **Türkçe** (Ana dil)
- **Arapça** (Gelecek sürüm)
- **İngilizce** (Gelecek sürüm)

Dil dosyaları `assets/translations/` klasöründe bulunur.

## 📦 Kullanılan Paketler

### UI ve Tasarım
- `flutter_localizations`: Çoklu dil desteği
- `easy_localization`: Kolay lokalizasyon
- `flutter_animate`: Animasyonlar
- `lottie`: Lottie animasyonları

### State Management
- `flutter_riverpod`: State management
- `flutter_hooks`: React-style hooks
- `hooks_riverpod`: Riverpod + Hooks entegrasyonu

### Navigasyon
- `go_router`: Declarative routing

### Network ve API
- `dio`: HTTP client
- `retrofit`: Type-safe HTTP client
- `json_annotation`: JSON serialization

### Depolama
- `shared_preferences`: Basit key-value storage
- `hive`: NoSQL database
- `hive_flutter`: Hive Flutter entegrasyonu

### Medya ve Kamera
- `image_picker`: Fotoğraf/video seçimi
- `camera`: Kamera kontrolü
- `video_player`: Video oynatma

### Konum ve Haritalar
- `google_maps_flutter`: Google Maps entegrasyonu
- `geolocator`: Konum servisleri
- `geocoding`: Adres çevirme

### İzinler
- `permission_handler`: Uygulama izinleri

## 🔧 Geliştirme

### Code Generation
Modeller ve servisler için code generation kullanılır:
```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Yeni Dil Ekleme
1. `assets/translations/` klasörüne yeni dil dosyası ekleyin
2. `lib/main.dart` dosyasında `supportedLocales` listesine ekleyin
3. Gerekli çevirileri tamamlayın

### Yeni Özellik Ekleme
1. `lib/features/` altında yeni klasör oluşturun
2. Feature-based architecture'ı takip edin
3. Gerekli route'ları `app_router.dart` dosyasına ekleyin

## 🚀 Deployment

### Android
```bash
flutter build apk --release
# veya
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 İletişim

Proje hakkında sorularınız için:
- Email: [email]
- GitHub Issues: [repository-url]/issues

---

**Not**: Bu uygulama demo amaçlıdır. Gerçek kullanım için backend API entegrasyonu ve güvenlik önlemleri eklenmelidir.
