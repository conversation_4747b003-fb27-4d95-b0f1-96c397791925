<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> - De<PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #1976D2, #42A5F5);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .phone-container {
            width: 375px;
            height: 667px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #1976D2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .app-content {
            height: calc(100% - 44px);
            display: flex;
            flex-direction: column;
        }

        .login-screen {
            flex: 1;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: #1976D2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 20px rgba(25, 118, 210, 0.3);
        }

        .logo-icon {
            font-size: 60px;
            color: white;
        }

        .app-title {
            font-size: 32px;
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 10px;
        }

        .app-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 40px;
            text-align: center;
        }

        .form-group {
            width: 100%;
            margin-bottom: 20px;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1976D2;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: #1976D2;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background: #1565C0;
        }

        .register-link {
            color: #1976D2;
            text-decoration: none;
            font-weight: 500;
        }

        .home-screen {
            display: none;
            flex: 1;
            flex-direction: column;
        }

        .app-bar {
            background: #1976D2;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .app-bar h1 {
            font-size: 20px;
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .welcome-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .report-btn {
            width: 100%;
            height: 120px;
            background: #1976D2;
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        .violation-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .violation-card {
            background: white;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .violation-card:hover {
            transform: translateY(-2px);
        }

        .violation-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 10px;
            font-size: 24px;
            color: white;
        }

        .bottom-nav {
            background: white;
            border-top: 1px solid #E0E0E0;
            display: flex;
            height: 60px;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            color: #666;
            font-size: 12px;
            transition: color 0.3s;
        }

        .nav-item.active {
            color: #1976D2;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .demo-note {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="demo-note">🎯 Mürşid Demo - Flutter Uygulaması Önizlemesi</div>

    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>🔋 100%</span>
        </div>

        <div class="app-content">
            <!-- Login Screen -->
            <div class="login-screen" id="loginScreen">
                <div class="logo">
                    <div class="logo-icon">🛡️</div>
                </div>
                <h1 class="app-title">Mürşid</h1>
                <p class="app-subtitle">Hoş Geldiniz</p>

                <div class="form-group">
                    <input type="email" class="form-input" placeholder="E-posta" value="<EMAIL>">
                </div>

                <div class="form-group">
                    <input type="password" class="form-input" placeholder="Şifre" value="demo123">
                </div>

                <button class="login-btn" onclick="showHome()">Giriş Yap</button>

                <p>Hesabınız yok mu? <a href="#" class="register-link">Kayıt Ol</a></p>
            </div>

            <!-- Home Screen -->
            <div class="home-screen" id="homeScreen">
                <div class="app-bar">
                    <h1>Mürşid</h1>
                    <span>🔔</span>
                </div>

                <div class="main-content">
                    <div class="welcome-card">
                        <h3>Hoş Geldiniz</h3>
                        <p>Toplumsal güvenlik için birlikte çalışalım. İhlalleri bildirerek hem topluma katkıda bulunun hem de ödül kazanın.</p>
                    </div>

                    <button class="report-btn">
                        <span style="font-size: 48px;">📷</span>
                        <span>Şimdi İhbar Et</span>
                    </button>

                    <h3 style="margin-bottom: 15px;">İhlal Türleri</h3>
                    <div class="violation-grid">
                        <div class="violation-card">
                            <div class="violation-icon" style="background: #D32F2F;">🚗</div>
                            <div>Trafik İhlali</div>
                        </div>
                        <div class="violation-card">
                            <div class="violation-icon" style="background: #795548;">🧹</div>
                            <div>Temizlik İhlali</div>
                        </div>
                        <div class="violation-card">
                            <div class="violation-icon" style="background: #7B1FA2;">⚠️</div>
                            <div>Taciz</div>
                        </div>
                        <div class="violation-card">
                            <div class="violation-icon" style="background: #E65100;">⚡</div>
                            <div>Şiddet</div>
                        </div>
                        <div class="violation-card">
                            <div class="violation-icon" style="background: #455A64;">🔊</div>
                            <div>Gürültü</div>
                        </div>
                        <div class="violation-card">
                            <div class="violation-icon" style="background: #5D4037;">🅿️</div>
                            <div>Park İhlali</div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 12px;">
                        <div class="welcome-card" style="flex: 1; text-align: center;">
                            <div style="color: #1976D2; font-size: 24px; font-weight: bold;">12</div>
                            <div style="font-size: 12px; color: #666;">Toplam İhbar</div>
                        </div>
                        <div class="welcome-card" style="flex: 1; text-align: center;">
                            <div style="color: #388E3C; font-size: 24px; font-weight: bold;">₺245</div>
                            <div style="font-size: 12px; color: #666;">Kazanç</div>
                        </div>
                    </div>
                </div>

                <div class="bottom-nav">
                    <div class="nav-item active">
                        <div class="nav-icon">🏠</div>
                        <div>Ana Sayfa</div>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">📋</div>
                        <div>İhbarlarım</div>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">💰</div>
                        <div>Cüzdan</div>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">👤</div>
                        <div>Profil</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showHome() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('homeScreen').style.display = 'flex';
        }

        let currentScreen = 'login';
        let currentNavItem = 0;

        function showHome() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('homeScreen').style.display = 'flex';
            currentScreen = 'home';
        }

        function showReportScreen() {
            alert('📷 Kamera açılıyor...\n\n✅ Bu demo versiyonunda kamera simülasyonu\n📱 Gerçek uygulamada kamera ve galeri erişimi olacak');
        }

        function showReports() {
            const mainContent = document.querySelector('.main-content');
            mainContent.innerHTML = `
                <h2 style="margin-bottom: 20px;">İhbarlarım</h2>
                <div class="welcome-card" style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <div style="width: 40px; height: 40px; background: #D32F2F; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; margin-right: 15px;">🚗</div>
                        <div style="flex: 1;">
                            <div style="font-weight: bold;">Trafik İhlali</div>
                            <div style="font-size: 12px; color: #666;">2 gün önce</div>
                        </div>
                        <div style="background: #4CAF50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Onaylandı</div>
                    </div>
                    <div style="font-size: 14px; color: #666; margin-bottom: 10px;">Kırmızı ışıkta geçen araç</div>
                    <div style="background: #E8F5E8; padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                        <span style="color: #4CAF50; margin-right: 8px;">💰</span>
                        <span style="color: #4CAF50; font-weight: bold;">Kazanç: ₺200.00</span>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <div style="width: 40px; height: 40px; background: #795548; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; margin-right: 15px;">🧹</div>
                        <div style="flex: 1;">
                            <div style="font-weight: bold;">Temizlik İhlali</div>
                            <div style="font-size: 12px; color: #666;">5 saat önce</div>
                        </div>
                        <div style="background: #2196F3; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">İnceleniyor</div>
                    </div>
                    <div style="font-size: 14px; color: #666;">Çöp atma ihlali</div>
                </div>

                <div class="welcome-card">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <div style="width: 40px; height: 40px; background: #5D4037; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; margin-right: 15px;">🅿️</div>
                        <div style="flex: 1;">
                            <div style="font-weight: bold;">Park İhlali</div>
                            <div style="font-size: 12px; color: #666;">1 hafta önce</div>
                        </div>
                        <div style="background: #F44336; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Reddedildi</div>
                    </div>
                    <div style="font-size: 14px; color: #666; margin-bottom: 10px;">Engelli park yerine park etme</div>
                    <div style="background: #FFEBEE; padding: 10px; border-radius: 8px; display: flex; align-items: center;">
                        <span style="color: #F44336; margin-right: 8px;">ℹ️</span>
                        <span style="color: #F44336; font-size: 12px;">Yeterli kanıt bulunamadı</span>
                    </div>
                </div>
            `;
            updateNavigation(1);
        }

        function showWallet() {
            const mainContent = document.querySelector('.main-content');
            mainContent.innerHTML = `
                <h2 style="margin-bottom: 20px;">Cüzdan</h2>

                <div style="display: flex; gap: 12px; margin-bottom: 20px;">
                    <div class="welcome-card" style="flex: 1; text-align: center;">
                        <div style="color: #388E3C; font-size: 32px; margin-bottom: 8px;">💰</div>
                        <div style="color: #388E3C; font-size: 24px; font-weight: bold;">₺1,250.00</div>
                        <div style="font-size: 12px; color: #666;">Toplam Kazanç</div>
                    </div>
                    <div class="welcome-card" style="flex: 1; text-align: center;">
                        <div style="color: #1976D2; font-size: 32px; margin-bottom: 8px;">💳</div>
                        <div style="color: #1976D2; font-size: 24px; font-weight: bold;">₺850.00</div>
                        <div style="font-size: 12px; color: #666;">Kullanılabilir Bakiye</div>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="color: #FF9800; font-size: 24px; margin-right: 15px;">⏳</div>
                        <div>
                            <div style="font-weight: bold;">Bekleyen Kazanç</div>
                            <div style="color: #FF9800; font-size: 20px; font-weight: bold;">₺400.00</div>
                        </div>
                    </div>
                </div>

                <button style="width: 100%; padding: 15px; background: #388E3C; color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; margin-bottom: 20px;" onclick="withdrawMoney()">
                    💸 Para Çek
                </button>

                <h3 style="margin-bottom: 15px;">İşlem Geçmişi</h3>

                <div class="welcome-card" style="margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <div style="color: #4CAF50; font-size: 20px; margin-right: 10px;">➕</div>
                            <div>
                                <div style="font-weight: bold;">Trafik İhlali Ödülü</div>
                                <div style="font-size: 12px; color: #666;">1 gün önce</div>
                            </div>
                        </div>
                        <div style="color: #4CAF50; font-weight: bold;">+₺200.00</div>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <div style="color: #F44336; font-size: 20px; margin-right: 10px;">➖</div>
                            <div>
                                <div style="font-weight: bold;">Para Çekme</div>
                                <div style="font-size: 12px; color: #666;">3 gün önce</div>
                            </div>
                        </div>
                        <div style="color: #F44336; font-weight: bold;">-₺150.00</div>
                    </div>
                </div>

                <div class="welcome-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <div style="color: #4CAF50; font-size: 20px; margin-right: 10px;">➕</div>
                            <div>
                                <div style="font-weight: bold;">Temizlik İhlali Ödülü</div>
                                <div style="font-size: 12px; color: #666;">5 gün önce</div>
                            </div>
                        </div>
                        <div style="color: #4CAF50; font-weight: bold;">+₺100.00</div>
                    </div>
                </div>
            `;
            updateNavigation(2);
        }

        function showProfile() {
            const mainContent = document.querySelector('.main-content');
            mainContent.innerHTML = `
                <div style="text-align: center; margin-bottom: 30px;">
                    <div style="width: 100px; height: 100px; background: #1976D2; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 50px;">👤</div>
                    <h2>Ahmet İssam</h2>
                    <div style="color: #666;"><EMAIL></div>
                </div>

                <div style="display: flex; justify-content: space-around; margin-bottom: 30px;">
                    <div style="text-align: center;">
                        <div style="color: #1976D2; font-size: 24px; font-weight: bold;">12</div>
                        <div style="font-size: 12px; color: #666;">İhbar</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #1976D2; font-size: 24px; font-weight: bold;">₺1,250</div>
                        <div style="font-size: 12px; color: #666;">Kazanç</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #1976D2; font-size: 24px; font-weight: bold;">85%</div>
                        <div style="font-size: 12px; color: #666;">Başarı</div>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 10px; cursor: pointer;" onclick="alert('Profil düzenleme sayfası açılacak')">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 15px; font-size: 20px;">✏️</span>
                            <span>Profili Düzenle</span>
                        </div>
                        <span>▶️</span>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 10px; cursor: pointer;" onclick="alert('Bildirim ayarları açılacak')">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 15px; font-size: 20px;">🔔</span>
                            <span>Bildirimler</span>
                        </div>
                        <span>▶️</span>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 10px; cursor: pointer;" onclick="showLanguageDialog()">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 15px; font-size: 20px;">🌐</span>
                            <span>Dil</span>
                        </div>
                        <span>▶️</span>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 10px; cursor: pointer;" onclick="alert('Karanlık mod: ' + (Math.random() > 0.5 ? 'Açık' : 'Kapalı'))">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 15px; font-size: 20px;">🌙</span>
                            <span>Karanlık Mod</span>
                        </div>
                        <span>🔄</span>
                    </div>
                </div>

                <div class="welcome-card" style="margin-bottom: 20px; cursor: pointer;" onclick="alert('Yardım sayfası açılacak')">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 15px; font-size: 20px;">❓</span>
                            <span>Yardım ve Destek</span>
                        </div>
                        <span>▶️</span>
                    </div>
                </div>

                <button style="width: 100%; padding: 15px; background: #F44336; color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: bold;" onclick="logout()">
                    🚪 Çıkış Yap
                </button>
            `;
            updateNavigation(3);
        }

        function updateNavigation(activeIndex) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, index) => {
                if (index === activeIndex) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        function withdrawMoney() {
            const amount = prompt('Çekilecek tutarı girin (₺):', '100');
            if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                alert(`✅ Para çekme talebiniz alındı!\n💰 Tutar: ₺${amount}\n⏰ İşlem 1-3 iş günü içinde tamamlanacak`);
            }
        }

        function showLanguageDialog() {
            const language = prompt('Dil seçin:\n1 - Türkçe\n2 - العربية\n3 - English\n\nSeçiminizi girin (1-3):', '1');
            const languages = {'1': 'Türkçe', '2': 'العربية', '3': 'English'};
            if (languages[language]) {
                alert(`✅ Dil değiştirildi: ${languages[language]}`);
            }
        }

        function logout() {
            if (confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
                document.getElementById('homeScreen').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'flex';
                currentScreen = 'login';
            }
        }

        // Navigation event listeners
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const navItems = document.querySelectorAll('.nav-item');
                navItems[0].addEventListener('click', () => {
                    location.reload(); // Reload to show home
                });
                navItems[1].addEventListener('click', showReports);
                navItems[2].addEventListener('click', showWallet);
                navItems[3].addEventListener('click', showProfile);

                // Add click events to violation cards and report button
                document.addEventListener('click', function(e) {
                    if (e.target.closest('.violation-card') || e.target.closest('.report-btn')) {
                        showReportScreen();
                    }
                });
            }, 1000);
        });

        // Auto demo after 3 seconds
        setTimeout(() => {
            if (document.getElementById('loginScreen').style.display !== 'none') {
                showHome();
            }
        }, 3000);
    </script>
</body>
</html>
